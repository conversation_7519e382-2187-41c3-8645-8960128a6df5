import URI from 'urijs';
import { CustomEventName } from '@/consts/custom-event-name';

// 获取日程详情页外链
export function getScheduleDetailUrl(event: Realtime.ExtractCustomEvent<CustomEventName.SEND_CARD>) {
  const uri = new URI(event.value.url.value);
  uri.segment(JSON.stringify(event.value.url.planId));
  uri.addQuery('e', event.value.id);
  uri.addQuery('sc', event.value.url.relateType);

  return uri.href();
}


export function openExternalLink(event: Realtime.ExtractCustomEvent<CustomEventName.SEND_CARD>) {
  native.navigator.v2.launchWebView(getScheduleDetailUrl(event));
}
