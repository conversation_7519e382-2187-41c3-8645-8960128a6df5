import type { Composer } from 'vue-i18n';
import { i18n } from '@/i18n';
import { Locale } from '@/config/locale';


/** @function
 * 当前系统语言
 * @returns 系统语言字符串
 */
export function currentLocale(): string {
  return (i18n.global as unknown as Composer).locale.value;
}

type GetLocaleStringFunc = () => string;

/** @function
 * 根据当前系统语言获取对应值
 * @param chnFn - 繁体中文值
 * @param engFn - 英文值
 * @returns 系统语言对应值
 */
export function valueByLocale(chnFn: GetLocaleStringFunc | string, engFn: GetLocaleStringFunc | string): string {
  if (currentLocale() === String(Locale.ZH_HK)) {
    return typeof chnFn === 'function' ? chnFn() : chnFn;
  }

  return typeof engFn === 'function' ? engFn() : engFn;
}

/** @function
 * 根据当前系统语言获取对应文本
 * @param chnText - 繁体中文文本
 * @param engText - 英文文本
 * @param  [fallback=false] - 当前语言为空白时fallback文本
 * @returns 系统语言对应文本
 */
export function textByLocale(chnText: string, engText: string, fallback = true): string {
  if (fallback) {
    return valueByLocale(() => (chnText || engText), () => (engText || chnText));
  }

  return valueByLocale(chnText, engText);
}

/**
 * App 语言标识转PC语言标识
 *
 * @param locale - APP locale
 * @returns Locale
 */
export function convertPCLocale(locale: Locale) {
  switch (locale) {
    case Locale.ZH_HK:
      return Locale.ZH_MO;

    case Locale.EN:
      return Locale.EN_US;

    default:
      return locale;
  }
}
