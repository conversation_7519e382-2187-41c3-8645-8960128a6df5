<script setup lang="ts">
import { textByLocale } from '@/helps/locale';

defineProps<{
  message: Chat.Message
}>();
</script>


<template>
  <div
    class="card-detail"
    :class="{ 'card-detail-clickable': true }"
    @click="message.payload!.click"
  >
    <div class="card-detail-content">
      <!-- 服务标签和状态 -->
      <div class="card-detail-header">
        <div class="card-detail-service">
          <span class="card-detail-service-label">{{ textByLocale(message.payload!.serviceName.title,
                                                                  message.payload!.serviceName.enTitle) }}</span>

          <!-- 状态标签（如果有） -->
          <!-- <div
            v-if="message.payload!.statusLabel"
            class="card-detail-status"
          >
            <div
              class="card-detail-status-dot"
              :style="{ backgroundColor: message.payload!.statusColor }"
            />
            <span class="card-detail-status-label">{{ message.payload!.statusLabel }}</span>
          </div> -->
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="card-detail-main">
        <!-- 标题 -->
        <div class="card-detail-title">
          <h3 class="card-detail-title-text">
            {{ message.payload!.title }}
          </h3>
        </div>

        <!-- 内容 -->
        <div
          v-if="message.payload!.otherItems && message.payload!.otherItems.length"
          class="card-detail-body"
        >
          <div class="card-detail-body-content">
            <template
              v-for="item in message.payload!.otherItems"
              :key="item.enKey"
            >
              <span>{{ textByLocale(item.key, item.enKey) }}:</span>
              <span>{{ item.value }}</span>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 箭头图标 -->
    <div
      v-if="true"
      class="card-detail-arrow"
    >
      <img
        src="@/assets/img/chat/icon-chevron-right.svg"
      >
    </div>
  </div>
</template>


<style scoped lang="less">
.card-detail {
  position: relative;
  display: flex;
  flex: 1;
  gap: 39px;
  align-items: stretch;
  justify-content: stretch;
  margin-top: 10px;
  margin-left: 45px;
  padding: 12px 12px 14px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 -1px 2px 0 #0000000f, 0 2px 4px 0 #00000024;

  &-clickable {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 8px 0 #00000029, 0 -1px 2px 0 #00000014;
    }

    &:active {
      transform: translateY(1px);
    }
  }

  &-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 4px;
  }

  &-header {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  &-service {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
  }

  &-service-label {
    color: #0086ff;
    font-weight: 500;
    font-size: 12px;
    font-family: 'PingFang SC', sans-serif;
    line-height: 1.4;
  }

  &-status {
    display: flex;
    gap: 4px;
    align-items: center;
    opacity: 0; // 根据Figma设计，状态标签默认隐藏
  }

  &-status-dot {
    width: 7px;
    height: 7px;
    background-color: #ff9a2e;
    border-radius: 50%;
  }

  &-status-label {
    color: #868ea1;
    font-weight: 400;
    font-size: 12px;
    font-family: 'PingFang HK', sans-serif;
    line-height: 1.3;
    text-transform: uppercase;
  }

  &-main {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &-title {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  &-title-text {
    margin: 0;
    color: #444;
    font-weight: 500;
    font-size: 16px;
    font-family: 'PingFang SC', sans-serif;
    line-height: 1.4;
  }

  &-body {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &-body-content {
    display: flex;
    gap: 6px;
    align-items: center;
    color: #888;
    font-weight: 400;
    font-size: 12px;
    font-family: 'PingFang SC', sans-serif;
    line-height: 1.4;
  }

  &-arrow {
    position: absolute;
    top: 35.5px;
    right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
