import { MessageType } from '@/consts/message-type';
import { RoleType } from '@/consts/role-type';
import { cardFactory } from './components/card';
import AssistantMessage from './assistant.vue';
import UserMessage from './user.vue';


type MessageFactoryReturn = typeof AssistantMessage | typeof UserMessage | ReturnType<typeof cardFactory> | null;

export function messageFactory(message: Chat.Message): MessageFactoryReturn {
  switch (message.type) {
    case MessageType.DIALOG:
      return message.role === RoleType.ASSISTANT ? AssistantMessage : UserMessage;

    case MessageType.CARD:
      return cardFactory();

    default:
      return null;
  }
}
