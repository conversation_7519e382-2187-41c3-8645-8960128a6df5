import { ref } from 'vue';
import { nanoid } from 'nanoid';
import { RoleType } from '@/consts/role-type';
import { MessageStatus } from '@/consts/message-status';
import { MessageType } from '@/consts/message-type';
import { log } from '@/helps/logger';

export function useMessage() {
  const messages = ref<Chat.Messages>([]);
  const currentAiReplyingMessage = ref<Chat.Message | null>(null);

  function addMessage(message: Partial<Chat.Message>): Chat.Message {
    const data = {
      id: nanoid(),
      type: MessageType.DIALOG,
      role: RoleType.ASSISTANT,
      content: '',
      status: MessageStatus.LOADING,
      createdAt: Date.now(),
      previousMessageId: undefined,
      payload: undefined,
      isUtilCall: false,
      ...message,
    };

    // 有previousMessageId则按previousMessageId顺序插入
    if (data.previousMessageId) {
      const index = messages.value.findIndex((msg) => msg.id === data.previousMessageId);
      log(`上一条消息位置:${index}--${messages.value[index]?.content}`);
      if (index !== -1) {
        messages.value.splice(index + 1, 0, data);
        return messages.value[index + 1];
      }
    }

    messages.value.push(data);
    return messages.value[messages.value.length - 1];
  }

  function removeMessage(messageId: string | number) {
    const index = messages.value.findIndex((msg) => msg.id === messageId);
    if (index !== -1) {
      if (currentAiReplyingMessage.value?.id === messageId) {
        currentAiReplyingMessage.value = null;
      }

      messages.value.splice(index, 1);
    }
  }

  // 根据ID查找消息
  function findMessageById(messageId: string | number): Chat.Message | undefined {
    return messages.value.find((msg) => msg.id === messageId);
  }

  // 追加消息内容（用于流式文本）
  function appendMessageContent(messageId: string | number, content: string): void {
    const message = findMessageById(messageId);
    if (message) {
      message.content += content;
    }
  }

  // 更新消息状态
  function updateMessageStatus(messageId: string | number, status: MessageStatus): void {
    const message = findMessageById(messageId);
    if (message) {
      message.status = status;
    }
  }

  // 添加用户消息
  function addUserMessage(content: string, messageId?: string, status: MessageStatus = MessageStatus.COMPLETE):
  Chat.Message {
    return addMessage({
      id: messageId ?? nanoid(),
      role: RoleType.USER,
      content,
      status,
    });
  }

  // 清理当前AI回复的消息
  function clearCurrentAiReplyingMessage() {
    currentAiReplyingMessage.value = null;
  }

  // 更新当前AI回复的消息
  function updateCurrentAiReplyingMessage(message: Partial<Chat.Message>) {
    if (currentAiReplyingMessage.value) {
      Object.assign(currentAiReplyingMessage.value, message);
    }
  }

  // 设置当前AI回复的消息
  function setCurrentAiReplyingMessage(message: Chat.Message) {
    currentAiReplyingMessage.value = message;
  }

  // 是否有当前AI回复的消息
  function hasCurrentAiReplyingMessage(): boolean {
    return !!currentAiReplyingMessage.value;
  }

  // 获取当前AI回复的消息
  function getCurrentAiReplyingMessage(): Chat.Message | null {
    return currentAiReplyingMessage.value;
  }

  // 情况content为空的message
  function clearEmptyMessage() {
    messages.value = messages.value.filter((msg) => {
      if (msg.type === MessageType.DIALOG) {
        return !!msg.content.trim();
      }

      return true;
    });
  }

  return {
    messages,

    addMessage,
    removeMessage,
    findMessageById,
    appendMessageContent,
    updateMessageStatus,
    addUserMessage,

    clearCurrentAiReplyingMessage,
    updateCurrentAiReplyingMessage,
    setCurrentAiReplyingMessage,
    hasCurrentAiReplyingMessage,
    getCurrentAiReplyingMessage,
    clearEmptyMessage,
  };
}
