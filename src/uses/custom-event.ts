import { ref } from 'vue';
import { MessageStatus } from '@/consts/message-status';
import { MessageType } from '@/consts/message-type';
import { CustomEventName } from '@/consts/custom-event-name';
import { AppOpenType } from '@/consts/app-open-type';
import { log } from '@/helps/logger';
import { openExternalLink } from '@/helps/open-external-link';
import type { useAudioRecorder } from './audio-recorder';
import type { useAudioPlayer } from './audio-player';
import type { useMessage } from './message';

export function useCustomEvent() {
  const isNotCurrentPage = ref(false);

  // 打开应用
  function openApp(
    audioRecorder: ReturnType<typeof useAudioRecorder>,
    audioPlayer: ReturnType<typeof useAudioPlayer>,
    event: Realtime.ExtractCustomEvent<CustomEventName.OPEN_APP>,
  ) {
    log(`打开应用: ${event.value.serviceCode}`);
    audioRecorder.stop();
    audioPlayer.stop();
    isNotCurrentPage.value = true;
    native.navigator.v2.launchService({
      serviceCode: event.value.serviceCode,
    });
  }

  // 拨打电话
  function callPhone(event:Realtime.ExtractCustomEvent<CustomEventName.CALL_PHONE>) {
    log(`拨打电话: ${event.value.phoneNo}`);
    native.phone.makeCall({
      type: event.value.phoneNoType,
      phoneNo: event.value.phoneNo,
    });
  }

  function openDetail(event: Realtime.ExtractCustomEvent<CustomEventName.SEND_CARD>) {
    switch (event.value.url.type) {
      case AppOpenType.EXTERNAL_APP:
      case AppOpenType.EXTERNAL_BROWSER:
        openExternalLink(event);
        break;

      case AppOpenType.INTERNAL_APP:
        native.navigator.v2.launchService({
          serviceCode: event.value.url.value,
          eventArgs: {
            dataId: event.value.id,
            category: event.value.url.category,
          },
        });
        break;

      default:
        break;
    }
  }

  // 发送卡片消息
  function sendCard(
    message: ReturnType<typeof useMessage>,
    event: Realtime.ExtractCustomEvent<CustomEventName.SEND_CARD>,
  ) {
    log('发送卡片消息');
    message.addMessage({
      type: MessageType.CARD,
      payload: {
        ...event.value,
        click: () => openDetail(event),
      },
      status: MessageStatus.COMPLETE,
    });
  }

  return {
    isNotCurrentPage,

    openApp,
    callPhone,
    sendCard,
  };
}
