import { ref } from 'vue';
import URI from 'urijs';
import _ from 'lodash';

import { MessageStatus } from '@/consts/message-status';
import { MessageType } from '@/consts/message-type';
import { RoleType } from '@/consts/role-type';

interface Params {
  insertData: (data: Chat.Message[]) => void;
}

interface HistoryResult {
  records: {
    id: number,
    dialogue_frm: RoleType,
    dialogue_content: string,
  }[],
  page_info: {
    next_cursor: number | null;
  },
}

async function fetchHistory(cursor: number | null, pageSize: number = 20): Promise<HistoryResult> {
  const uri = new URI(process.env.AI_MARY_API_BASE_URL);
  uri.segment('history');

  const res = await fetch(uri.href(), {
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
    method: 'POST',
    body: JSON.stringify({
      user_id: native.user.getUserId(),
      cursor,
      page_size: pageSize,
    }),
  });

  return res.json();
}

function handleGetData(data: HistoryResult): Chat.Message[] {
  const messages = _.get(data, 'records', []).reverse();

  return messages.map((item) => {
    return {
      id: item.id,
      type: MessageType.DIALOG,
      role: item.dialogue_frm,
      content: item.dialogue_content,
      status: MessageStatus.COMPLETE,
      createdAt: Date.now(),
    };
  });
}

export function useMessageHistory({
  insertData,
}: Params) {
  const cursor = ref<number | null>(0);
  const pageSize = ref(20);
  const total = ref(0);
  const loading = ref(false);
  const initLoading = ref(false);
  const finished = ref(false);
  const isError = ref(false);

  async function load() {
    if (finished.value) {
      initLoading.value = false;
      loading.value = false;
      return;
    }

    loading.value = true;

    if (total.value === 0 && !initLoading.value) {
      initLoading.value = true;
    } else {
      initLoading.value = false;
    }

    try {
      const res = await fetchHistory(cursor.value, pageSize.value);
      cursor.value = _.get(res, 'page_info.next_cursor', null);
      if (cursor.value === 0) {
        finished.value = true;
      }

      const messages = handleGetData(res);
      total.value += messages.length;

      insertData(messages);
    } catch (err) {
      isError.value = true;
      throw err;
    } finally {
      initLoading.value = false;
      loading.value = false;
    }
  }

  return {
    total,
    loading,
    initLoading,
    finished,
    isError,

    load,
  };
}
