/* eslint-disable no-param-reassign */
/**
 * 打字机配置接口
 */
export interface UseTypewriterConfig {
  /** 每个字符的显示延迟时间(毫秒)，默认为 150 */
  charDelay?: number;
  /** 是否启用打字机效果，默认为 true */
  enabled?: boolean;
  /** 打字完成回调函数 */
  onComplete?: (messageId: string | number) => void;
  /** 打字机停止回调函数 */
  onStop?: (messageId: string | number) => void;
  /** 内容更新回调函数 */
  onContentUpdate?: (messageId: string | number, content: string) => void;
}

/**
 * 单个打字任务的状态接口
 */
interface TypewriterTask {
  /** 任务ID（消息ID） */
  messageId: string | number;
  /** 完整文本缓冲区 */
  fullText: string;
  /** 当前显示的字符索引 */
  currentIndex: number;
  /** 是否正在打字 */
  isTyping: boolean;
  /** 是否已完成 */
  isComplete: boolean;
  /** 是否已标识文本增量完成 */
  isContextComplete: boolean;
  /** 定时器ID */
  timerId: number | null;
}

/**
 * 打字机管理 composable
 * 支持管理多个并发的打字任务
 */
export function useTypewriter(config: UseTypewriterConfig = {}) {
  const {
    charDelay = 150,
    enabled = true,
    onComplete,
    onStop,
    onContentUpdate,
  } = config;

  // 使用 Map 管理多个打字任务
  const tasks = new Map<string | number, TypewriterTask>();

  /**
   * 清理指定任务的定时器
   */
  function clearTimer(task: TypewriterTask): void {
    if (task.timerId !== null) {
      clearTimeout(task.timerId);
      task.timerId = null;
    }
  }

  /**
   * 更新指定任务的消息内容
   */
  function updateMessage(task: TypewriterTask): void {
    const content = task.fullText.substring(0, task.currentIndex);
    onContentUpdate?.(task.messageId, content);
  }

  /**
   * 打字下一个字符
   */
  function typeNextChar(task: TypewriterTask): void {
    if (!task.isTyping || task.isComplete) {
      return;
    }

    // 检查是否已经打完所有字符
    if (task.currentIndex >= task.fullText.length) {
      // 只有在文本增量完成且打完所有字符时，才标记完成和停止打字
      if (task.isContextComplete) {
        task.isComplete = true;
        task.isTyping = false; // 只有在真正完成时才设置为 false
        const { messageId } = task;

        // 清理任务
        tasks.delete(messageId);

        // 触发完成回调
        onComplete?.(messageId);
      }
    } else {
    // 显示下一个字符
      task.currentIndex += 1;
      updateMessage(task);
    }

    // 设置下一个字符的定时器
    task.timerId = setTimeout(() => {
      task.timerId = null;
      typeNextChar(task);
    }, charDelay) as unknown as number;
  }

  /**
   * 开始指定任务的打字动画
   */
  function startTyping(task: TypewriterTask): void {
    if (!enabled || task.isComplete || task.isTyping) {
      return;
    }

    task.isTyping = true;
    typeNextChar(task);
  }

  /**
   * 为指定消息启动打字机
   * @param message - 消息对象
   * @returns 消息对象
   */
  function start(message: Chat.Message): Chat.Message {
    if (!message.id) {
      return message;
    }

    // 停止已存在的任务（如果有）
    const existingTask = tasks.get(message.id);
    if (existingTask) {
      clearTimer(existingTask);
    }

    // 创建新任务
    const task: TypewriterTask = {
      messageId: message.id,
      fullText: '',
      currentIndex: 0,
      isTyping: false,
      isComplete: false,
      isContextComplete: false,
      timerId: null,
    };

    // 存储任务
    tasks.set(message.id, task);

    // 初始化消息内容为空
    onContentUpdate?.(message.id, '');

    return message;
  }

  /**
   * 向指定消息添加增量文本内容
   * @param deltaText - 增量文本
   * @param messageId - 消息ID，如果不提供则使用当前活跃的任务
   */
  function addContext(deltaText: string, messageId?: string | number): void {
    if (!deltaText) {
      return;
    }

    // 如果没有指定 messageId，尝试使用最后一个任务
    let targetMessageId = messageId;
    if (!targetMessageId) {
      const taskEntries = Array.from(tasks.entries());
      if (taskEntries.length === 0) {
        return;
      }
      // 使用最后一个任务
      [targetMessageId] = taskEntries[taskEntries.length - 1];
    }

    const task = tasks.get(targetMessageId);
    if (!task) {
      return;
    }

    // 添加到完整文本缓冲区
    task.fullText += deltaText;

    if (!enabled) {
      // 如果禁用打字机效果，直接显示
      onContentUpdate?.(task.messageId, task.fullText);
      return;
    }

    // 如果当前没有在打字，开始打字
    if (!task.isTyping && !task.isComplete) {
      startTyping(task);
    }
  }

  /**
   * 标记指定消息的文本增量完成
   * 标识没有更多文本增量，让打字机自然完成剩余文本的打印
   * @param messageId - 消息ID，如果不提供则使用最后一个任务
   */
  function completeContext(messageId?: string | number): void {
    // 如果没有指定 messageId，尝试使用最后一个任务
    let targetMessageId = messageId;
    if (!targetMessageId) {
      const taskEntries = Array.from(tasks.entries());
      if (taskEntries.length === 0) {
        return;
      }
      // 使用最后一个任务
      [targetMessageId] = taskEntries[taskEntries.length - 1];
    }

    const task = tasks.get(targetMessageId);
    if (!task) {
      return;
    }

    // 标记文本增量完成
    task.isContextComplete = true;

    // 如果当前没有在打字且已经打完所有字符，立即触发完成
    if (!task.isTyping && task.currentIndex >= task.fullText.length && !task.isComplete) {
      task.isComplete = true;
      const { messageId: taskMessageId } = task;

      // 清理任务
      tasks.delete(taskMessageId);

      // 触发完成回调
      onComplete?.(taskMessageId);
    }
  }

  /**
   * 停止指定消息的打字机实例
   * 中断打字效果，保持当前已显示的文本
   *
   * @param messageId - 消息ID，如果不提供则停止所有任务
   * @param full - 是否显示完全文本
   */
  function stop(messageId?: string | number, full: boolean = true): void {
    if (messageId) {
      // 停止指定任务
      const task = tasks.get(messageId);
      if (task) {
        clearTimer(task);
        task.isTyping = false;
        task.isComplete = true;
        task.isContextComplete = false;

        if (full) {
          onContentUpdate?.(task.messageId, task.fullText);
        }

        // 清理任务
        const taskMessageId = task.messageId;
        tasks.delete(messageId);

        onStop?.(taskMessageId);
      }
    } else {
      // 停止所有任务
      const taskEntries = Array.from(tasks.entries());
      taskEntries.forEach(([, task]) => {
        clearTimer(task);
        task.isTyping = false;
        task.isComplete = true;
        task.isContextComplete = false;

        if (full) {
          onContentUpdate?.(task.messageId, task.fullText);
        }

        onStop?.(task.messageId);
      });
      tasks.clear();
    }
  }

  /**
   * 清理所有打字机实例
   * 完全清理所有状态和任务
   */
  function cleanup(): void {
    // 清理所有任务的定时器
    const taskEntries = Array.from(tasks.entries());
    taskEntries.forEach(([, task]) => {
      clearTimer(task);
    });

    // 清空所有任务
    tasks.clear();
  }

  /**
   * 获取是否有任务正在打字
   * @param messageId - 可选的消息ID，如果提供则检查指定消息的打字状态
   */
  function getIsTyping(messageId?: string | number): boolean {
    if (messageId) {
      const task = tasks.get(messageId);
      return task?.isTyping ?? false;
    }

    // 检查是否有任何任务正在打字
    const taskEntries = Array.from(tasks.entries());
    return taskEntries.some(([, task]) => task.isTyping);
  }

  function getIsContextComplete(messageId?: string | number): boolean {
    if (messageId) {
      const task = tasks.get(messageId);
      return task?.isContextComplete ?? false;
    }

    // 检查是否有任何任务已完成文本增量
    const taskEntries = Array.from(tasks.entries());
    return taskEntries.some(([, task]) => task.isContextComplete);
  }

  return {
    start,
    addContext,
    completeContext,
    stop,
    cleanup,
    getIsTyping,
    getIsContextComplete,
  };
}
