import type { RoleType } from '@/consts/role-type';
import type { MessageType } from '@/consts/message-type';
import type { Align } from '@/consts/align';
import type { MessageStatus } from '@/consts/message-status';


declare global {
  namespace Chat {
    type Messages = Message[];

    interface Message {
      id: string | number;
      role: RoleType;
      content: string;
      createdAt: number;
      status: MessageStatus;
      isUtilCall?: boolean;
      align?: Align;
      type?: MessageType;
      previousMessageId?: string;
      payload?: Realtime.SendCardEventValue & {
        click?: () => void;
      };
    }
  }
}

export {};
